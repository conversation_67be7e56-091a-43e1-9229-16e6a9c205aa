import src.Gui.callProcess as callProcess
from pydantic import BaseModel
from typing import Dict, Any, List
from datetime import datetime
import src.base.settings as settings
import src.base.cache as cache
from fastapi import HTTPException
from src.web.http import app  
from fastapi import Request
import logging
import traceback
import src.Gui.callProcess as callF
shared_queue = callProcess.shared_queue
@app.get("/api/get-message")
async def get_message():
    try:
        if shared_queue and not shared_queue.empty():
            message_data = shared_queue.get()
            if isinstance(message_data, dict) and "消息" in message_data:
                message_text = message_data["消息"]
                return {"code":200,"message":message_text}
        else:
            return {"code":200,"message":"not need refresh"}
    except Exception as e:
        logging.error(f"Error in get-message: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/start-function")
async def start_function(request: Request):
    try:
        data = await request.json()
        if callF.thisProcess.p is not None:
            callF.thisProcess.terminate()
        callF.thisProcess.run(data)
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in start-function: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/stop-function")
async def stop_function(request: Request):
    try:
        if callF.thisProcess.p is not None:
            callF.thisProcess.terminate()
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in stop-function: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/get-default-date")
async def get_default_date():
    try:
        return {"code":200,"message":"success","startDate1":cache.Lastdate,"endDate1":cache.Nowdate,"startDate2":cache.theYearFirstDay,"endDate2":cache.Nowdate}
    except Exception as e:
        logging.error(f"Error in get-default-date: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/machine-info")
async def machine_info():
    try:
        data = {"machineCode":callF.regInstance.machine_code,"expiryTime":callF.regInstance.expire_time}
        return data
    except Exception as e:
        logging.error(f"Error in machine-info: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/manual/guide")
async def manual_guide():
    try:
        import webbrowser
        webbrowser.open('https://q2t4357ads.feishu.cn/wiki/H2GHwBZjFiIkF4kKxFTclmTanTg?from=from_copylink')
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in manual-guide: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/register")
async def register(request: Request):
    try:
        data = await request.json()
        newExpireTime=callF.regInstance.register(data["registrationCode"])
        return {"code":200,"message":"success","expiryTime":newExpireTime}
    except Exception as e:
        logging.error(f"Error in register: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/labor-dispatch/template")
async def labor_dispatch_template():
    try:
        from src.utils.fipQuickFill.salary import laborDispatchTemplate
        return {"code":200,"message":"success","data":laborDispatchTemplate()}
    except Exception as e:
        logging.error(f"Error in labor-dispatch-template: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/labor-dispatch/update")
async def labor_dispatch_update(request: Request):
    try:
        data = await request.json()
        from src.utils.fipQuickFill.salary import laborDispatchUpdate
        laborDispatchUpdate(data)
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in labor-dispatch-update: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/treasury-in-transit/template")
async def treasury_in_transit_template():
    try:
        from src.utils.fundCapital.paymentInTransitQuery import treasuryInTransitTemplate
        return {"code":200,"message":"success","data":treasuryInTransitTemplate()}
    except Exception as e:
        logging.error(f"Error in treasury-in-transit-template: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/treasury-in-transit/update")
async def treasury_in_transit_update(request: Request):
    try:
        data = await request.json()
        from src.utils.fundCapital.paymentInTransitQuery import treasuryInTransitUpdate
        treasuryInTransitUpdate(data)
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in treasury-in-transit-update: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/query-voucher-log")
async def query_voucher_log(request: Request):
    try:
        data = await request.json()
        from src.utils.fundCapital.paymentInTransitQuery import queryData
        queryData()
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in query-voucher-log: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/report-assistant/select-last-year-path")
async def select_last_year_path(request: Request):
    try:
        import src.utils.fileui as fileui
        path=fileui.select_file()
        cache.wirteLastYearFinancialReport(path)
        return {"code":200,"message":"success","path":path}
    except Exception as e:
        logging.error(f"Error in select-last-year-path: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/report-assistant/select-current-year-path")
async def select_current_year_path(request: Request):
    try:
        import src.utils.fileui as fileui
        path=fileui.select_file()
        cache.wirteCurrentYearFinancialReport(path)
        return {"code":200,"message":"success","path":path}
    except Exception as e:
        logging.error(f"Error in select-last-year-path: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/import-excel-update-db")
async def import_excel_update_db(request: Request):
    try:
        data = await request.json()
        from src.utils.DB.updateDb import updateToDb
        updateToDb(data["tableName"],True)
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in import-excel-update-db: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/get-sap-path")
async def get_sap_path(request: Request):
    try:
        from src.utils.enhance.sapMsg import get_sap_path
        path=get_sap_path()['saplogon_path'].replace("saplogon.exe","sapshcut.exe")
        return {"code":200,"message":"success","path":path}
    except Exception as e:
        logging.error(f"Error in get-sap-path: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))