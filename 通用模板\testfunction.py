import sys
import os
import time
import re
sys.path.append(".")
import src.utils.Excel
import src.utils.sapPublic
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import src.utils.Excel.openpyxlExcel
import src.utils.Browser.Browser as myBrowser
import src.utils.dopReport.财商自动同步应收账款台账
import src.utils.fipOther.财务一体化自动物资结算2
import src.utils.DB.creatDuckdbTable
import src.utils.dopReport.moneyIOLedger
import src.utils.fundCapital.财务一体化自动下载资金计划
import duckdb
import src.base.settings as settings
import src.utils.DB.outputSQL  as Sql
import pandas as pd
import src.utils.sapPublic.sapExport
import tkinter.filedialog
import src.utils.DB.readtxttolist
import src.utils.closeAccountingPeriod.obtainData
import src.utils.dopReport.collect
import src.utils.fundCapital.财务一体化自动调整比例
from src.utils.sapPublic.GetSAPSession import creatSAP
import subprocess
from src.utils.financialStatement.complete import complete
from src.utils.dopReport.compareData import main
from src.utils.fundCapital.财务一体化自动下载资金计划 import cachePlan
if __name__ == '__main__':
    import multiprocessing
    multiprocessing.freeze_support()
    from src.web.http import start_server
    process=start_server()
    import src.Gui.callProcess as callProcess #导入开始创建函数进程
    from src.Gui.FletGui.register_dialog import RegisterDialog #创建注册对象
    from src.Gui.register import initLock
    initLock()
    print("服务器启动成功")
    process.join()
    






