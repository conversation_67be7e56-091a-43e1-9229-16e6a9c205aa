"""
全局变量
"""
import os
import sys

from datetime import datetime
from src.base.model import RegisterConf
from datetime import datetime, timedelta


YEAR=datetime.now().year
MONTH=datetime.now().month


# 获取上一个月月初第一天的字符串
first_day_of_current_month = datetime.now().replace(day=1)
last_day_of_last_month = first_day_of_current_month - timedelta(days=1)
first_day_of_last_month = last_day_of_last_month.replace(day=1)
LAST_MONTH_FIRST_DAY = first_day_of_last_month.strftime("%Y-%m-%d")
# 获取上一个月最后一天的字符串
LAST_MONTH_LAST_DAY = last_day_of_last_month.strftime("%Y-%m-%d")


if getattr(sys, 'frozen', False):
    # 如果是打包后的 exe 文件
    PATH_EXE = os.path.dirname(sys.executable)
else:
    PATH_EXE = os.path.dirname(os.path.dirname(os.path.realpath(sys.argv[0])))
    #PATH_EXE = os.path.dirname(os.path.dirname(sys.executable))

# 数据文件路径
PATH_DATA = os.path.join(PATH_EXE, 'data')
PATH_INTERNAL = os.path.join(PATH_EXE, 'data/InternalData')
PATH_DUCKDB = os.path.join(PATH_INTERNAL, 'Duckdb')
PATH_DOWNLOAD = os.path.join(PATH_INTERNAL, '导出数据')
PATH_THIRD_APP=os.path.join(PATH_INTERNAL, 'ThirdApp')
PATH_QUERY=os.path.join(PATH_DATA, '数据查询结果')
PATH_CONFIG=os.path.join(PATH_DATA, '配置文件')
PATH_EXCEL=os.path.join(PATH_DATA, 'Excel中间文件')
PATH_FIP_DOWNLOAD=os.path.join(PATH_DATA, '中台导出单据附件')
CACHE_PATH= os.path.join(PATH_INTERNAL, 'cache')

# 静态资源路径
PATH_STATIC = os.path.join(PATH_EXE, 'static')
# 默认图标
# 注册相关，启动时修改
REG = RegisterConf()

