
import src.base.settings as settings
import src.utils.DB.midIntrSQLiteDB as midIntrSQLiteDB
from src.utils.DB.midIntrSQLiteDB import excelDB
import pandas as pd

def closeDataProcess(data:list):
    pass




def saveDataProcess(data):
    conn=midIntrSQLiteDB.webDB()
    conn.writeData("表格缓存",data)
    conn.close()

def deleteDataProcess(data):
    conn=midIntrSQLiteDB.webDB()
    conn.deleteData("表格缓存",data)
    conn.close()
def saveDataProcess2(data):
    conn=midIntrSQLiteDB.webDB()
    conn.updateLastData("表格缓存",data)
    conn.close()

def getDataProcess(request):
    conn=midIntrSQLiteDB.webDB()
    primaryID=request["periodId"]
    data=conn.queryData("表格缓存",primaryID)
    conn.close()
    return data

def queryDatalist():
    conn=midIntrSQLiteDB.webDB()
    data=conn.queryDatalist("表格缓存")
    conn.close()
    return data


def pushDataToColse(datas:dict):
    conn=excelDB()
    for key,value in datas.items():
        #修正空白列
        for i in range(len(value[0])):
            if value[0][i] is None or value[0][i]=="" or value[0][i]==" ":
                value[0][i]="unnamed_"+str(i)
        df=pd.DataFrame(value[1:],columns=value[0])
        df.to_sql(key,conn.conn,if_exists="replace",index=False)
    conn.close()
        
def getCloseDataProcess():
    conn=excelDB()
    df1=pd.read_sql("select * from 独立结账模板安全费",conn.conn)
    df2=pd.read_sql("select * from 独立结账模板收入成本",conn.conn)
    conn.close()
    return [[df1.columns.tolist()]+df1.values.tolist(),[df2.columns.tolist()]+df2.values.tolist()]