import winreg

def get_sap_path():
    """从注册表获取SAP的安装路径"""
    try:
        # 打开指定的注册表项
        reg_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\saplogon.exe"
        key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path, 0, winreg.KEY_READ)
        
        # 读取默认值（完整的saplogon.exe路径）
        default_value, _ = winreg.QueryValueEx(key, "")
        
        # 读取Path值（SAP安装目录）
        path_value, _ = winreg.QueryValueEx(key, "Path")
        
        # 关闭注册表项
        winreg.CloseKey(key)
        
        return {
            "saplogon_path": default_value,
            "install_directory": path_value
        }
        
    except FileNotFoundError:
        return "未找到SAP相关注册表项，可能未安装SAP或路径未被记录"
    except PermissionError:
        return "权限不足，请以管理员身份运行脚本"
    except Exception as e:
        return f"获取路径时发生错误: {str(e)}"


if __name__ == "__main__":
    sap_info = get_sap_path()
    if isinstance(sap_info, dict):
        print("SAP路径信息:")
        print(f"saplogon.exe完整路径: {sap_info['saplogon_path']}")
    else:
        print(f"错误: {sap_info}")
