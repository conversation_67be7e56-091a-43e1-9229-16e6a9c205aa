
import sys
import multiprocessing
import os
script=os.path.abspath(__file__)
sPath=os.path.dirname(script)
sys.path.append(sPath+"/ftools.zip")
sys.path.append(sPath+"/Lib/site-packages/pywin32_system32")
home=os.path.dirname(script)
os.chdir(home)
print(sPath)
if __name__ == "__main__":  
    if not hasattr(sys, 'frozen'):
        sys.frozen = True
    multiprocessing.freeze_support()
    import src.main_webview
    src.main_webview.main()

